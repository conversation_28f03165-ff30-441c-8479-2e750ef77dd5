import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
  Alert,
} from 'react-native';
import {Images} from '../assets';

import {ContactNumber} from '../screens/companyDetails/companyDetailsScreen';
import WebSocketVoteService from '../socket/iccappush.js';

interface Props {
  numberData?: ContactNumber;
}

const CustomTollFreeNumbersCard = ({numberData}: Props) => {
  const [isVoting, setIsVoting] = useState(false);

  const handlePhoneCall = (number: string) => {
    const phoneNumber = `tel:${number}`;
    Linking.openURL(phoneNumber).catch((err: Error) =>
      console.error('Failed to open phone call:', err),
    );
  };

  const handleUpVote = async () => {
    if (isVoting || !numberData?.number) return;

    setIsVoting(true);
    try {
      console.log('👍 User tapped upvote for phone number:', numberData.number);
      await WebSocketVoteService.sendVote(numberData.number, true);
      Alert.alert('Success', 'Your upvote has been recorded!');
    } catch (error) {
      console.error('❌ Failed to send upvote:', error);

      // Show different messages based on error type
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('timeout')) {
        Alert.alert(
          'Connection Timeout',
          'The server is taking too long to respond. Your vote has been queued and will be sent when connection is restored.',
        );
      } else if (errorMessage.includes('Connection failed')) {
        Alert.alert(
          'Connection Error',
          'Unable to connect to the voting server. Your vote has been queued and will be sent when connection is restored.',
        );
      } else {
        Alert.alert('Error', 'Failed to record your vote. Please try again.');
      }
    } finally {
      setIsVoting(false);
    }
  };

  const handleDownVote = async () => {
    if (isVoting || !numberData?.number) return;

    setIsVoting(true);
    try {
      console.log(
        '👎 User tapped downvote for phone number:',
        numberData.number,
      );
      await WebSocketVoteService.sendVote(numberData.number, false);
      Alert.alert('Success', 'Your downvote has been recorded!');
    } catch (error) {
      console.error('❌ Failed to send downvote:', error);

      // Show different messages based on error type
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('timeout')) {
        Alert.alert(
          'Connection Timeout',
          'The server is taking too long to respond. Your vote has been queued and will be sent when connection is restored.',
        );
      } else if (errorMessage.includes('Connection failed')) {
        Alert.alert(
          'Connection Error',
          'Unable to connect to the voting server. Your vote has been queued and will be sent when connection is restored.',
        );
      } else {
        Alert.alert('Error', 'Failed to record your vote. Please try again.');
      }
    } finally {
      setIsVoting(false);
    }
  };

  const formatVoteCount = (count: number): string => {
    if (count < 1000) {
      return count.toString();
    }
    return (count / 1000).toFixed(1) + 'k';
  };
  return (
    <View style={styles.card}>
      <View style={styles.titleView}>
        <Text style={[styles.titleText]}>{numberData?.description}</Text>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}>
          <Image style={styles.phoneIcon} source={Images.ic_phoneCall} />
          <TouchableOpacity
            onPress={() => handlePhoneCall(numberData?.number ?? '')}>
            <Text style={[styles.phoneNumber]}>{numberData?.number}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.generalInfo}>
          <TouchableOpacity
            onPress={handleUpVote}
            disabled={isVoting}
            style={[
              styles.iconContainer,
              isVoting && styles.disabledContainer,
            ]}>
            <Image style={styles.upVoteIcon} source={Images.ic_arrowUp} />
            <Text style={styles.upVoteText}>
              {' '}
              {formatVoteCount(numberData?.upvoteCount || 0)}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleDownVote}
            disabled={isVoting}
            style={[
              styles.iconContainer,
              isVoting && styles.disabledContainer,
            ]}>
            <Image style={styles.upVoteIcon} source={Images.ic_arrowDown} />
            <Text style={styles.upVoteText}>
              {formatVoteCount(numberData?.downvoteCount || 0)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'column',
    alignItems: 'stretch',
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    borderColor: '#D7E2F1',
    justifyContent: 'space-evenly',
  },
  titleText: {
    fontFamily: 'Poppins-Bold',
    fontSize: 17,
  },
  titleView: {
    margin: 10,
  },
  phoneNumber: {
    fontSize: 17,
    fontFamily: 'Poppins-Medium',
    textDecorationLine: 'underline',
    color: '#0066cc',
  },
  phoneIcon: {
    marginBottom: 3,
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  generalInfo: {
    paddingTop: 5,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    width: 200,
  },
  iconContainer: {
    backgroundColor: '#D9E2EF',
    padding: 4,
    borderRadius: 6,
    width: 60,
    height: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  upVoteIcon: {
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  upVoteText: {
    marginTop: 1,
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    marginRight: 5,
  },
  disabledContainer: {
    opacity: 0.5,
  },
});

export default CustomTollFreeNumbersCard;
