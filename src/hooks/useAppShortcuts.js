import {useState, useEffect, useCallback} from 'react';
import {Alert, Platform} from 'react-native';
import AppShortcutService from '../services/AppShortcutService';
import bookmarkStorageService from '../services/bookmarkStorageService';

export const useAppShortcuts = navigation => {
  const [isShortcutSupported, setIsShortcutSupported] = useState(false);
  const [existingShortcuts, setExistingShortcuts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Check if shortcuts are supported on device initialization
  useEffect(() => {
    const checkSupport = async () => {
      if (Platform.OS === 'android') {
        try {
          const supported = await AppShortcutService.isShortcutSupported();
          setIsShortcutSupported(supported);
        } catch (error) {
          console.error('Error checking shortcut support:', error);
          setIsShortcutSupported(false);
        }
      }
    };

    checkSupport();
  }, []);

  // Set up deep link listener
  useEffect(() => {
    if (Platform.OS === 'android' && navigation) {
      const handleDeepLink = url => {
        console.log('Deep link received:', url);
        AppShortcutService.handleDeepLinkNavigation(url, navigation);
      };

      AppShortcutService.setupDeepLinkListener(handleDeepLink);

      return () => {
        AppShortcutService.removeDeepLinkListener();
      };
    }
  }, [navigation]);

  // Load existing shortcuts
  const loadExistingShortcuts = useCallback(async () => {
    if (Platform.OS !== 'android') return;

    try {
      const shortcuts = await AppShortcutService.getExistingShortcuts();
      setExistingShortcuts(shortcuts);
    } catch (error) {
      console.error('Error loading existing shortcuts:', error);
    }
  }, []);

  // Create company list shortcut
  const createCompanyListShortcut = useCallback(
    async (categoryId, categoryName) => {
      if (!isShortcutSupported) {
        Alert.alert(
          'Not Supported',
          'App shortcuts are not supported on this device.',
        );
        return false;
      }

      setIsLoading(true);
      try {
        const result = await AppShortcutService.createCompanyListShortcut(
          categoryId,
          categoryName,
        );
        console.log('Shortcut creation result:', result);

        Alert.alert(
          'Shortcut Requested',
          `A shortcut for "${categoryName} Companies" has been requested. Please check your home screen and confirm the shortcut creation if prompted.`,
          [
            {text: 'OK'},
            {
              text: 'Check Home Screen',
              onPress: () => {
                // This will minimize the app so user can check home screen
                if (Platform.OS === 'android') {
                  // Move app to background
                  try {
                    require('react-native').BackHandler.exitApp();
                  } catch (e) {
                    console.log('Could not minimize app');
                  }
                }
              },
            },
          ],
        );

        // Reload existing shortcuts
        await loadExistingShortcuts();
        return true;
      } catch (error) {
        console.error('Error creating company list shortcut:', error);

        let errorMessage = 'Failed to create shortcut. Please try again.';
        if (error.message.includes('NOT_SUPPORTED')) {
          errorMessage = 'Pin shortcuts are not supported on this device.';
        } else if (error.message.includes('NO_ACTIVITY')) {
          errorMessage = 'App is not in foreground. Please try again.';
        }

        Alert.alert('Error', `${errorMessage}\n\nDetails: ${error.message}`);
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [isShortcutSupported, loadExistingShortcuts],
  );

  // Create categories shortcut
  const createCategoriesShortcut = useCallback(async () => {
    if (!isShortcutSupported) {
      Alert.alert(
        'Not Supported',
        'App shortcuts are not supported on this device.',
      );
      return false;
    }

    setIsLoading(true);
    try {
      await AppShortcutService.createCategoriesShortcut();

      Alert.alert(
        'Shortcut Created',
        'A shortcut for "Categories" has been added to your home screen.',
        [{text: 'OK'}],
      );

      // Reload existing shortcuts
      await loadExistingShortcuts();
      return true;
    } catch (error) {
      console.error('Error creating categories shortcut:', error);

      let errorMessage = 'Failed to create shortcut. Please try again.';
      if (error.message.includes('NOT_SUPPORTED')) {
        errorMessage = 'Pin shortcuts are not supported on this device.';
      }

      Alert.alert('Error', errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isShortcutSupported, loadExistingShortcuts]);

  const createCompanyShortcut = useCallback(
    async (companyId, companyName) => {
      if (!isShortcutSupported) {
        Alert.alert(
          'Not Supported',
          'Pin shortcuts are not supported on this device.',
        );
        return false;
      }

      setIsLoading(true);
      try {
        const result = await AppShortcutService.createCompanyShortcut(
          companyId,
          companyName,
        );
        console.log('Company shortcut creation result:', result);

        // Store shortcut information in bookmark storage
        const shortcutId = `company_${companyId}`;
        await bookmarkStorageService.addShortcut(companyId, shortcutId);
        await bookmarkStorageService.updateShortcutStatus(
          companyId,
          true,
          shortcutId,
        );

        Alert.alert(
          'Shortcut Requested',
          `A shortcut for "${companyName}" has been requested. Please check your home screen and confirm the shortcut creation if prompted.`,
          [
            {text: 'OK'},
            {
              text: 'Check Home Screen',
              onPress: () => {
                // This will minimize the app so user can check home screen
                if (Platform.OS === 'android') {
                  // Move app to background
                  try {
                    require('react-native').BackHandler.exitApp();
                  } catch (e) {
                    console.log('Could not minimize app');
                  }
                }
              },
            },
          ],
        );

        // Reload existing shortcuts
        await loadExistingShortcuts();
        return true;
      } catch (error) {
        console.error('Error creating company shortcut:', error);

        let errorMessage = 'Failed to create shortcut. Please try again.';
        if (error.message.includes('NOT_SUPPORTED')) {
          errorMessage = 'Pin shortcuts are not supported on this device.';
        }

        Alert.alert('Error', errorMessage);
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [isShortcutSupported, loadExistingShortcuts],
  );

  // Remove shortcut
  const removeShortcut = useCallback(
    async shortcutId => {
      if (!isShortcutSupported) return false;

      setIsLoading(true);
      try {
        await AppShortcutService.removeShortcut(shortcutId);

        // Extract company ID from shortcut ID and update storage
        if (shortcutId.startsWith('company_')) {
          const companyId = parseInt(shortcutId.replace('company_', ''));
          if (!isNaN(companyId)) {
            await bookmarkStorageService.removeShortcut(companyId);
            await bookmarkStorageService.updateShortcutStatus(companyId, false);
          }
        }

        Alert.alert('Shortcut Removed', 'The shortcut has been removed.', [
          {text: 'OK'},
        ]);

        // Reload existing shortcuts
        await loadExistingShortcuts();
        return true;
      } catch (error) {
        console.error('Error removing shortcut:', error);
        Alert.alert('Error', 'Failed to remove shortcut. Please try again.');
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [isShortcutSupported, loadExistingShortcuts],
  );

  // Remove company shortcut by company ID
  const removeCompanyShortcut = useCallback(
    async companyId => {
      const shortcutId = `company_${companyId}`;
      return await removeShortcut(shortcutId);
    },
    [removeShortcut],
  );

  // Check if a specific shortcut exists
  const shortcutExists = useCallback(
    shortcutId => {
      return existingShortcuts.some(shortcut => shortcut.id === shortcutId);
    },
    [existingShortcuts],
  );

  return {
    isShortcutSupported,
    existingShortcuts,
    isLoading,
    createCompanyListShortcut,
    createCategoriesShortcut,
    createCompanyShortcut,
    removeShortcut,
    removeCompanyShortcut,
    loadExistingShortcuts,
    shortcutExists,
  };
};
